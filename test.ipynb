{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e118216f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!DOCTYPE html><html lang=\"en-US\"><head><title>Just a moment...</title><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\"><meta name=\"robots\" content=\"noindex,nofollow\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><style>*{box-sizing:border-box;margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%;color:#313131;font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}body{display:flex;flex-direction:column;height:100vh;min-height:100vh}.main-content{margin:8rem auto;max-width:60rem;padding-left:1.5rem}@media (width <= 720px){.main-content{margin-top:4rem}}.h2{font-size:1.5rem;font-weight:500;line-height:2.25rem}@media (width <= 720px){.h2{font-size:1.25rem;line-height:1.5rem}}#challenge-error-text{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+);background-repeat:no-repeat;background-size:contain;padding-left:34px}@media (prefers-color-scheme:dark){body{background-color:#222;color:#d9d9d9}}</style><meta http-equiv=\"refresh\" content=\"360\"></head><body><div class=\"main-wrapper\" role=\"main\"><div class=\"main-content\"><noscript><div class=\"h2\"><span id=\"challenge-error-text\">Enable JavaScript and cookies to continue</span></div></noscript></div></div><script>(function(){window._cf_chl_opt={cvId: '3',cZone: \"dl.acm.org\",cType: 'managed',cRay: '959c2346f8e01828',cH: 'pIMnp1g5yU5CslWbg1jbplAtEXkUkqQDWOC5YRhR3oQ-1751606052-*******-P4J4ZRFpqH7uPa.ApDuCyKRNwDbV_5lXfjP6PxUiOH5hi1uA.vgRxXsxo2t025AP',cUPMDTk: \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_tk=VMdkduro8mQxKnNyipycGq_cl7Q4W8kjsvUjgM1WQv8-1751606052-*******-2I5DZJ43H.LwLeEqTLqqAyEsmlHxcdymer1MKnZdflw\",cFPWv: 'b',cITimeS: '1751606052',cTplC: 0,cTplV: 5,cTplB: 'cf',fa: \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_f_tk=VMdkduro8mQxKnNyipycGq_cl7Q4W8kjsvUjgM1WQv8-1751606052-*******-2I5DZJ43H.LwLeEqTLqqAyEsmlHxcdymer1MKnZdflw\",md: \"C1oEBgHsEzz8zQOnpXUIOJ3DIdKPnEFuduQYxLG7cms-1751606052-*******-Q_YKpv7v3WLOfcA9uyZeYFE7Zpiw89LZ6HTALuR4qxfpW.CkR64DS_ElXQRpxLYpre9kIQAU0.x.RZywiy4cibgCa7kzKBdHQggUUVWzTg6kGIHuml42OqldY802LEnTWbs834eS7UU1_wwDG11r9D.68CSAwWLAK9w6RTe.lSmqYwNqftrz6so_37kU2ViVUkJxIiK0RJBAE8Cx86_GpI8SFwYl66H4I.gz74FrgEXPF9YRohPQvvAUXU5b7IIWwaQx4BDIE_FCEzcqCk5ena41O0wDTylY_FOsIZ2LTVLg5ZasUPNVPNjMVLaTBTYR77s9TTdN0KJeGeKZKqfledcO6xpdDfHdQ42UKCxbF9EDcFCgYDXLYudf2RckDwnnScF4eSagN.oXu3GFZdocGOHvfUeyKcWWUe1oyiWvPBfC1wOoBvuA3AZiVb_z_MBbRpHI3ivJ_gX4FIejIbTCD_FKKgqwKLnibFqkcdjT8JJ3uTRrppOofNp4Ed_mNl8NTxVJOqh5g_kEbs.iuWJ6xNL16XgbU5mdq6hM7WH.8o9d5A5tSjVzUW0WS5hcv6lySyqo0EM_45Spa9x8V8QAPzcCLuWIe8qKyT4iXAN9d7dss9n6rIuTisvWg.kTjM5zTalQi64iuj7nG4FpAukE2uE2zP6X8PbwrQftX54VTgw4yh_IFyfNcmsePNLoMqD15RKvYbJXfZeMMHIUvJ5muTN2YqPW1pFVkpjNub41t4_1xXt_lgIj6x48b9AJx0.nOTbWCA7Xh7oWZ3kEbdoUJ.Zz3w1Y7GxtzdAHR_bkaBRijbl5ZLy_fvylneB_VTZn_eyP_6aj99bmvhmQdNtf5QMzkxaaptjOMybW9jHVMCf4n7dAWA5CT66zzQp1SaFtfkd.qC52Hx2D4XJ2qwXpmla5unbqhphsTOEq7yrBiqu.7I0MZzHbDZLqfKU6ZRdnMzxcLTF2PyHpP6OwFnxRrTGE5HcK2geRDpaw009DFqU\",mdrd: \"lBFWEopV7gq.F2tWeg5RgUFqMTfFzP65CD75uBM_O10-1751606052-*******-TNvwjvg2_niTk2n82..9ue8.vjD28bdO0YlW62nh8Nb.Pqt3UHuY2MR.uU4TFtye.tlYOcsHZlKXv.9WEWuF.vS.r6IqeEqgYIgbIi_KIyrAvYz7YKfKnHmHcb_7Ts7mF7ouxAUyM1gsfHmOFssi4hzLh0_E7ZS9usu8WQEU8.H8zKyxeHkoiXF3L2QHdb5Te_eoSA0vLzEIBDtrs9T3P2BIwRI7Ad5fH4_x1xmxPqsiXBXzCDng_0Ylm6EeA7hqrcWVXN2lp5OGtZMIjzuNPpNRkegFcGVyjj5JYdKkK6Pn26PWnUmYbLIwhtQVbjGqpFlC5zlU3ahnjzHE0zxdO6YjkYV7MFhN.AUshvc2aL3NnFVlq0sWUIceFEVRs82FpoG2aD4kALoP4OSuuL8I8N6_8VUWChKStUsbphfaPQMIKRd73rWPdQZx9YkZ0INL9ivUr2XjRbAxbZwZVwTnK1F7vPwmEOO_Wdalud1Tov6vpwfMBTD.ixpWVB4bRr3OKOCVd6xXuIK5xNgmOYuB8K82OVSXV5FYwbyGlG.0RCSmBBNPzz.2isDaurekgEUBxQJJUuYuVYJb4Do8tn47uUdO1WNGhdedZzhWKYo2P0J.P.y_f.LMlAe.zkg9YXylAQDl9R2pHLj2CngBEdkGdxLqTf0fKDwPvFyHKtobaCCGQ68fqHw8LpqDBN6OY4EBcrJzAJ1.99PySTtJbrqm9835ujMvQVDAovnIS9ajk9Jwv2wmr.bdy0bovOuUbKKmeHmrxTXd4ZVQkRn68gz_4LvMCiVM3JHMQ75rINuS8Qt0uOQinr_w3TdCwzWXxPRewNf3HweEh8Fee5BGtIa41LdaRv1.NddwM9O_mQEqpdX_aWtLOmQfMZk506uxVN2OaMhv_2Z753tCewXr1tTMiCgAUiRwcJp.g9ia6hIdvahtx9lbqUQFUZUHs6_eSJkp.ai.cb9i1VgAVmL9MTb3EPNzxVDiPhHi7f1sKXN3_0rH_sKlPvXU6.vHvpVYBPoiB4lor0JH9NUo8yHQc22GSspHcYEQdtHJ_VjlGzRuAj.7k5DQ.DmdA4pADvPQxql7aMHRCirnWrb.nGujFeQFIwPZz9J216nMOjp0nsbUHZ.3w4lXzKsmBJHZ6W5RhAh.w8lIO6ZgJHm5fTlMQjVNaLBlDaptPToSUwKRZ16ioXGRVZr2fJ1gWijCgpc2i7q.OCHCAmJLbDepGN3GSwmm8hnqu6B0SizrKJHFSuF8.B9XcZZKQ50Vhu7_hlmh9sMJm6Tb9Ox1rEVAvEKCZ.RkeuuoNt.IiubB7uh38keJNgXnTSoxfMpDNTnjgnz5G3_4k3w4VJcpz.NjAEwCiz2em1pJq4UWhJChjJX9UAjnNy0ubpqSGCQm6i5SVMNvOMPQF1vP8SW4VaKJBUmLnUF7msnjqmJDHGX91HOmTwmN4XKX.KFMsZXBx0oguD8w543HNcHpkcGhJ4d3BQExMwlQO1TrUnQ8wpwddejpK3.14wILgD.hffeXJzy44.tFQNMRt_iruM0RtWCHpINS8wDjQSrm6Bn5tOOLIThy8LtN9gS3ADdH.MsapeWqY3DRc0LzJAVL2OfE8b7lXlM69Ua0x.EYvuDEHrjnB_NrL0wQZ6ECUw7MNpBfMEyIvuN7YtWCiiOkWW_M9E2WFEsr..taQ89CN84IoMIMXOKS.jtdDV8qtFbSNpO9WJaQzTZOZeie5ihL3bTBu_5zg0WFc.cLJSKqQYfInaghLWduuxksQj.IzUY98WUv9mgjd8ujErwk6L_GbIQo6uXz6Wia3.IpYeK1PGITn3D.j3nyHM4d0CtnZ0Xvh743b4Fi31kAZgh21CzJWpp5Q6wr6cnTeTIRDIy8ooejFO5fcTPyyN_KyrdrpJLO_Y47NoYxFCEpYOQoGRLR7mbbt1v6a.nwbhqWNPbgtq8OwFn3hEDW5mzfT9LIiuokskxqq3D2Bcejki3Hmbe5L3SXrbVZ_r5riUjhXXWUkP33ZxH3nd1r2ner5TRZ2vKpBm2cTc0B6mCVXF2DAfh65TBn4LCJSvy4pEWC6FtM8zkENwBgfLKzVShYFpw_I1G6nLg3R0h5NxgdxNw3P4uj9loOCxCASMASHqQYSnQgT35PDaXkkyOts8YBzXbab.p_1Xh1EJAscKlz2inHpKRb093BTMiPaYclqSkSfA5NVZpHx0HRrHHp1qgT.eB1y_MvmFJugVpObU1HxXNtjZjuB.flVG.CtEG2GtFE30Kh0mBkhr1UNsG5EtOFIWcST2ImnpMPsxyY.iCB_275\",};var a = document.createElement('script');a.src = '/cdn-cgi/challenge-platform/h/b/orchestrate/chl_page/v1?ray=959c2346f8e01828';window._cf_chl_opt.cOgUHash = location.hash === '' && location.href.indexOf('#') !== -1 ? '#' : location.hash;window._cf_chl_opt.cOgUQuery = location.search === '' && location.href.slice(0, location.href.length - window._cf_chl_opt.cOgUHash.length).indexOf('?') !== -1 ? '?' : location.search;if (window.history && window.history.replaceState) {var ogU = location.pathname + window._cf_chl_opt.cOgUQuery + window._cf_chl_opt.cOgUHash;history.replaceState(null, null, \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_rt_tk=VMdkduro8mQxKnNyipycGq_cl7Q4W8kjsvUjgM1WQv8-1751606052-*******-2I5DZJ43H.LwLeEqTLqqAyEsmlHxcdymer1MKnZdflw\" + window._cf_chl_opt.cOgUHash);a.onload = function() {history.replaceState(null, null, ogU);}}document.getElementsByTagName('head')[0].appendChild(a);}());</script></body></html>\n"]}], "source": ["\n", "import requests\n", "\n", "\n", "target_url = \"https://dl.acm.org/doi/abs/10.1145/3679018\"\n", "\n", "\n", "response = requests.get(target_url)\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b957c0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Meta-GPS++: Enhancing Graph Meta-Learning with Contrastive Learning and Self-Training [{'ORCID': 'https://orcid.org/0000-0001-8621-7144', 'authenticated-orcid': False, 'given': '<PERSON><PERSON>', 'family': '<PERSON>', 'sequence': 'first', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}, {'ORCID': 'https://orcid.org/0000-0001-9327-434X', 'authenticated-orcid': False, 'given': '<PERSON>gyu', 'family': 'Li', 'sequence': 'additional', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}, {'ORCID': 'https://orcid.org/0000-0001-8190-5087', 'authenticated-orcid': False, 'given': '<PERSON><PERSON>', 'family': '<PERSON>', 'sequence': 'additional', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}, {'ORCID': 'https://orcid.org/0000-0003-3233-3777', 'authenticated-orcid': False, 'given': 'Lan', 'family': 'Huang', 'sequence': 'additional', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}, {'ORCID': 'https://orcid.org/0000-0002-5903-6150', 'authenticated-orcid': False, 'given': 'Fausto', 'family': 'Giunchiglia', 'sequence': 'additional', 'affiliation': [{'name': 'University of Trento, Trento, Italy'}]}, {'ORCID': 'https://orcid.org/0000-0002-1147-3968', 'authenticated-orcid': False, 'given': 'Yanchun', 'family': 'Liang', 'sequence': 'additional', 'affiliation': [{'name': 'Zhuhai Laboratory of the Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, Zhuhai College of Science and Technology, Zhuhai, China'}]}, {'ORCID': 'https://orcid.org/0000-0003-3954-1333', 'authenticated-orcid': False, 'given': 'Xiaoyue', 'family': 'Feng', 'sequence': 'additional', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}, {'ORCID': 'https://orcid.org/0000-0002-7162-7826', 'authenticated-orcid': False, 'given': 'Renchu', 'family': 'Guan', 'sequence': 'additional', 'affiliation': [{'name': 'Key Laboratory of Symbolic Computation and Knowledge Engineering of the Ministry of Education, College of Computer Science and Technology, Jilin University, Changchun, China'}]}] <jats:p>\n", "            Node classification is an essential problem in graph learning. However, many models typically obtain unsatisfactory performance when applied to few-shot scenarios. Some studies have attempted to combine meta-learning with graph neural networks to solve few-shot node classification on graphs. Despite their promising performance, some limitations remain. First, they employ the node encoding mechanism of homophilic graphs to learn node embeddings, even in heterophilic graphs. Second, existing models based on meta-learning ignore the interference of randomness in the learning process. Third, they are trained using only limited labeled nodes within the specific task, without explicitly utilizing numerous unlabeled nodes. Finally, they treat almost all sampled tasks equally without customizing them for their uniqueness. To address these issues, we propose a novel framework for few-shot node classification called\n", "            <jats:italic>Meta-GPS</jats:italic>\n", "            <jats:inline-formula content-type=\"math/tex\">\n", "              <jats:tex-math notation=\"LaTeX\" version=\"MathJax\">\\(++\\)</jats:tex-math>\n", "            </jats:inline-formula>\n", "            . Specifically, we first adopt an efficient method to learn discriminative node representations on homophilic and heterophilic graphs. Then, we leverage a prototype-based approach to initialize parameters and contrastive learning for regularizing the distribution of node embeddings. Moreover, we apply self-training to extract valuable information from unlabeled nodes. Additionally, we adopt S\n", "            <jats:inline-formula content-type=\"math/tex\">\n", "              <jats:tex-math notation=\"LaTeX\" version=\"MathJax\">\\({}^{2}\\)</jats:tex-math>\n", "            </jats:inline-formula>\n", "            (scaling and shifting) transformation to learn transferable knowledge from diverse tasks. The results on real-world datasets show the superiority of Meta-GPS\n", "            <jats:inline-formula content-type=\"math/tex\">\n", "              <jats:tex-math notation=\"LaTeX\" version=\"MathJax\">\\(++\\)</jats:tex-math>\n", "            </jats:inline-formula>\n", "            . Our code is available\n", "            <jats:ext-link xmlns:xlink=\"http://www.w3.org/1999/xlink\" ext-link-type=\"uri\" xlink:href=\"https://github.com/KEAML-JLU/Meta-GPS-Plus\">here</jats:ext-link>\n", "            .\n", "          </jats:p>\n"]}], "source": ["import requests\n", "\n", "doi = \"10.1145/3679018\"\n", "headers = {\n", "    \"Accept\": \"application/vnd.citationstyles.csl+json\"  # JSON 格式元数据\n", "}\n", "r = requests.get(f\"https://doi.org/{doi}\", headers=headers, timeout=10)\n", "r.raise_for_status()\n", "data = r.json()\n", "print(data[\"title\"], data[\"author\"], data[\"abstract\"])"]}, {"cell_type": "code", "execution_count": 3, "id": "b9704ed1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["页面部分源码预览: <html lang=\"en-US\" dir=\"ltr\"><head><title>请稍候…</title><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge\"><meta name=\"robots\" content=\"noindex,nofollow\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><style>*{box-sizing:border-box;margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%;color:#313131;font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}body{display:flex;flex-direction:column;height:100vh;min-height:100vh}.main-content{margin:8rem auto;max-width:60rem;padding-left:1.5rem}@media (width <= 720px){.main-content{margin-top:4rem}}.h2{font-size:1.5rem;font-weight:500;line-height:2.25rem}@media (width <= 720px){.h2{font-size:1.25rem;line-height:1.5rem}}#challenge-error-text{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+);background-repeat:no-repeat;background-size:contain;padding-left:34px}@media (prefers-color-scheme:dark){body{background-color:#222;color:#d9d9d9}}</style><meta http-equiv=\"refresh\" content=\"360\"><script src=\"/cdn-cgi/challenge-platform/h/b/orchestrate/chl_page/v1?ray=95a3e1be2b8beb2c\"></script><style>*{box-sizing:border-box;margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%;color:#313131;font-family:system-ui,-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}button{font-family:system-ui,-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,\"Noto Sans\",sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\"}body{display:flex;flex-direction:column;height:100vh;min-height:100vh}body.theme-dark{background-color:#222;color:#d9d9d9}body.theme-dark a{color:#fff}body.theme-dark a:hover{text-decoration:underline;color:#ee730a}body.theme-dark .lds-ring div{border-color:#999 rgba(0,0,0,0) rgba(0,0,0,0)}body.theme-dark .font-red{color:#b20f03}body.theme-dark .ctp-button{background-color:#4693ff;color:#1d1d1d}body.theme-dark #challenge-success-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDI2IDI2Ij48cGF0aCBmaWxsPSIjZDlkOWQ5IiBkPSJNMTMgMGExMyAxMyAwIDEgMCAwIDI2IDEzIDEzIDAgMCAwIDAtMjZtMCAyNGExMSAxMSAwIDEgMSAwLTIyIDExIDExIDAgMCAxIDAgMjIiLz48cGF0aCBmaWxsPSIjZDlkOWQ5IiBkPSJtMTAuOTU1IDE2LjA1NS0zLjk1LTQuMTI1LTEuNDQ1IDEuMzg1IDUuMzcgNS42MSA5LjQ5NS05LjYtMS40Mi0xLjQwNXoiLz48L3N2Zz4\")}body.theme-dark #challenge-error-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+\")}body.theme-light{background-color:#fff;color:#313131}body.theme-light a{color:#0051c3}body.theme-light a:hover{text-decoration:underline;color:#ee730a}body.theme-light .lds-ring div{border-color:#595959 rgba(0,0,0,0) rgba(0,0,0,0)}body.theme-light .font-red{color:#fc574a}body.theme-light .ctp-button{border-color:#003681;background-color:#003681;color:#fff}body.theme-light #challenge-success-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDI2IDI2Ij48cGF0aCBmaWxsPSIjMzEzMTMxIiBkPSJNMTMgMGExMyAxMyAwIDEgMCAwIDI2IDEzIDEzIDAgMCAwIDAtMjZtMCAyNGExMSAxMSAwIDEgMSAwLTIyIDExIDExIDAgMCAxIDAgMjIiLz48cGF0aCBmaWxsPSIjMzEzMTMxIiBkPSJtMTAuOTU1IDE2LjA1NS0zLjk1LTQuMTI1LTEuNDQ1IDEuMzg1IDUuMzcgNS42MSA5LjQ5NS05LjYtMS40Mi0xLjQwNXoiLz48L3N2Zz4=\")}body.theme-light #challenge-error-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI2ZjNTc0YSIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjZmM1NzRhIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+\")}a{transition:color 150ms ease;background-color:rgba(0,0,0,0);text-decoration:none;color:#0051c3}a:hover{text-decoration:underline;color:#ee730a}.main-content{margin:8rem auto;padding-right:1.5rem;padding-left:1.5rem;width:100%;max-width:60rem}.main-content .loading-verifying{height:76.391px}.spacer{margin:2rem 0}.spacer-top{margin-top:4rem}.spacer-bottom{margin-bottom:2rem}.heading-favicon{margin-right:.5rem;width:2rem;height:2rem}@media (width <= 720px){.main-content{margin-top:4rem}.heading-favicon{width:1.5rem;height:1.5rem}}.main-wrapper{display:flex;flex:1;flex-direction:column;align-items:center}.font-red{color:#b20f03}.h1{line-height:3.75rem;font-size:2.5rem;font-weight:500}.h2{line-height:2.25rem;font-size:1.5rem;font-weight:500}.core-msg{line-height:2.25rem;font-size:1.5rem;font-weight:400}.body-text{line-height:1.25rem;font-size:1rem;font-weight:400}@media (width <= 720px){.h1{line-height:1.75rem;font-size:1.5rem}.h2{line-height:1.5rem;font-size:1.25rem}.core-msg{line-height:1.5rem;font-size:1rem}}#challenge-error-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI2ZjNTc0YSIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjZmM1NzRhIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+\");background-repeat:no-repeat;background-size:contain;padding-left:34px}#challenge-success-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDI2IDI2Ij48cGF0aCBmaWxsPSIjMzEzMTMxIiBkPSJNMTMgMGExMyAxMyAwIDEgMCAwIDI2IDEzIDEzIDAgMCAwIDAtMjZtMCAyNGExMSAxMSAwIDEgMSAwLTIyIDExIDExIDAgMCAxIDAgMjIiLz48cGF0aCBmaWxsPSIjMzEzMTMxIiBkPSJtMTAuOTU1IDE2LjA1NS0zLjk1LTQuMTI1LTEuNDQ1IDEuMzg1IDUuMzcgNS42MSA5LjQ5NS05LjYtMS40Mi0xLjQwNXoiLz48L3N2Zz4=\");background-repeat:no-repeat;background-size:contain;padding-left:42px}.text-center{text-align:center}.ctp-button{transition-duration:200ms;transition-property:background-color,border-color,color;transition-timing-function:ease;margin:2rem 0;border:.063rem solid #0051c3;border-radius:.313rem;background-color:#0051c3;cursor:pointer;padding:.375rem 1rem;line-height:1.313rem;color:#fff;font-size:.875rem}.ctp-button:hover{border-color:#003681;background-color:#003681;cursor:pointer;color:#fff}.footer{margin:0 auto;padding-right:1.5rem;padding-left:1.5rem;width:100%;max-width:60rem;line-height:1.125rem;font-size:.75rem}.footer-inner{border-top:1px solid #d9d9d9;padding-top:1rem;padding-bottom:1rem}.clearfix::after{display:table;clear:both;content:\"\"}.clearfix .column{float:left;padding-right:1.5rem;width:50%}.diagnostic-wrapper{margin-bottom:.5rem}.footer .ray-id{text-align:center}.footer .ray-id code{font-family:monaco,courier,monospace}.core-msg,.zone-name-title{overflow-wrap:break-word}@media (width <= 720px){.diagnostic-wrapper{display:flex;flex-wrap:wrap;justify-content:center}.clearfix::after{display:initial;clear:none;text-align:center;content:none}.column{padding-bottom:2rem}.clearfix .column{float:none;padding:0;width:auto;word-break:keep-all}.zone-name-title{margin-bottom:1rem}}.loading-verifying{height:76.391px}.lds-ring{display:inline-block;position:relative;width:1.875rem;height:1.875rem}.lds-ring div{box-sizing:border-box;display:block;position:absolute;border:.3rem solid #595959;border-radius:50%;border-color:#313131 rgba(0,0,0,0) rgba(0,0,0,0);width:1.875rem;height:1.875rem;animation:lds-ring 1.2s cubic-bezier(.5, 0, .5, 1) infinite}.lds-ring div:nth-child(1){animation-delay:-.45s}.lds-ring div:nth-child(2){animation-delay:-.3s}.lds-ring div:nth-child(3){animation-delay:-.15s}@keyframes lds-ring{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.rtl .heading-favicon{margin-right:0;margin-left:.5rem}.rtl #challenge-success-text{background-position:right;padding-right:42px;padding-left:0}.rtl #challenge-error-text{background-position:right;padding-right:34px;padding-left:0}.challenge-content .loading-verifying{height:76.391px}@media (prefers-color-scheme: dark){body{background-color:#222;color:#d9d9d9}body a{color:#fff}body a:hover{text-decoration:underline;color:#ee730a}body .lds-ring div{border-color:#999 rgba(0,0,0,0) rgba(0,0,0,0)}body .font-red{color:#b20f03}body .ctp-button{background-color:#4693ff;color:#1d1d1d}body #challenge-success-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSIgdmlld0JveD0iMCAwIDI2IDI2Ij48cGF0aCBmaWxsPSIjZDlkOWQ5IiBkPSJNMTMgMGExMyAxMyAwIDEgMCAwIDI2IDEzIDEzIDAgMCAwIDAtMjZtMCAyNGExMSAxMSAwIDEgMSAwLTIyIDExIDExIDAgMCAxIDAgMjIiLz48cGF0aCBmaWxsPSIjZDlkOWQ5IiBkPSJtMTAuOTU1IDE2LjA1NS0zLjk1LTQuMTI1LTEuNDQ1IDEuMzg1IDUuMzcgNS42MSA5LjQ5NS05LjYtMS40Mi0xLjQwNXoiLz48L3N2Zz4\")}body #challenge-error-text{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgZmlsbD0ibm9uZSI+PHBhdGggZmlsbD0iI0IyMEYwMyIgZD0iTTE2IDNhMTMgMTMgMCAxIDAgMTMgMTNBMTMuMDE1IDEzLjAxNSAwIDAgMCAxNiAzbTAgMjRhMTEgMTEgMCAxIDEgMTEtMTEgMTEuMDEgMTEuMDEgMCAwIDEtMTEgMTEiLz48cGF0aCBmaWxsPSIjQjIwRjAzIiBkPSJNMTcuMDM4IDE4LjYxNUgxNC44N0wxNC41NjMgOS41aDIuNzgzem0tMS4wODQgMS40MjdxLjY2IDAgMS4wNTcuMzg4LjQwNy4zODkuNDA3Ljk5NCAwIC41OTYtLjQwNy45ODQtLjM5Ny4zOS0xLjA1Ny4zODktLjY1IDAtMS4wNTYtLjM4OS0uMzk4LS4zODktLjM5OC0uOTg0IDAtLjU5Ny4zOTgtLjk4NS40MDYtLjM5NyAxLjA1Ni0uMzk3Ii8+PC9zdmc+\")}}</style><script src=\"https://challenges.cloudflare.com/turnstile/v0/b/e7e9d014f96e/api.js?onload=RQJfI1&amp;render=explicit\" async=\"\" defer=\"\" crossorigin=\"anonymous\"></script></head><body><div class=\"main-wrapper\" role=\"main\"><div class=\"main-content\"><h1 class=\"zone-name-title h1\">dl.acm.org</h1><p id=\"bkObw1\" class=\"h2 spacer-bottom\">请完成以下操作，验证您是真人。</p><div id=\"NMOK7\" style=\"display: grid;\"><div><div><input type=\"hidden\" name=\"cf-turnstile-response\" id=\"cf-chl-widget-qjfok_response\"></div></div></div><div id=\"mqRZk3\" class=\"spacer loading-verifying\" style=\"display: none; visibility: hidden;\"><div class=\"lds-ring\"><div></div><div></div><div></div><div></div></div></div><div id=\"iNBLV6\" class=\"core-msg spacer spacer-top\">继续之前，dl.acm.org 需要先检查您的连接的安全性。</div><div id=\"ublC0\" style=\"display: none;\"><div id=\"challenge-success-text\" class=\"h2\">验证成功</div><div class=\"core-msg spacer\">正在等待 dl.acm.org 响应...</div></div><noscript><div class=\"h2\"><span id=\"challenge-error-text\">Enable JavaScript and cookies to continue</span></div></noscript></div></div><script>(function(){window._cf_chl_opt={cvId: '3',cZone: \"dl.acm.org\",cType: 'managed',cRay: '95a3e1be2b8beb2c',cH: 'J9ZxKjgEYS0.S61fKT2RGVFFk9R0hOODwUmScksHWBU-1751687254-*******-Dlln5dxF4y5K81LZ3qXgF84yRZ_4jr5mHv17vTOU5h4fZ_kpmWc_T9fPVYCqx6Ov',cUPMDTk: \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_tk=1TEKrWl2xawosfcFG6inA9s16e1_5HyK48LAlrUZsVw-1751687254-*******-RVHJzqUYnWbmlL_yu6wmzdgjOetvyqefbeqvk31F6kY\",cFPWv: 'b',cITimeS: '1751687254',cTplC: 0,cTplV: 5,cTplB: 'cf',fa: \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_f_tk=1TEKrWl2xawosfcFG6inA9s16e1_5HyK48LAlrUZsVw-1751687254-*******-RVHJzqUYnWbmlL_yu6wmzdgjOetvyqefbeqvk31F6kY\",md: \"1dcCWKFQiCrbyngxoNGSxpEFfOeaOdlA6TAx8lCGLQo-1751687254-*******-erHximD9mVtoXwj5MK.Swa_Sr7lnqMoImTuCWcDUR8I54qk0L89360h8BD3gGxcexREvy9hY7uQUm0w_b6.b5nEcV3am9pGM_rS_5VxsbAMLdeyDvhIQonx_9TcS.KdMcV0AbR4LpJOwAwLT88tS2YRKAXCftrHdSIJTmAAwEHeDz4DuPV.6XdM.7gcW773eD4fUpgw.ty78JVp8ZPR1KCVCLFDDuR_jzCX50chDKuPyAsLM69mxCOAhPVaAM4smhvf.w2xNMJ92_qHMfo_11e6cOmAOnhYXde8n8Bpmsh7nMZtIjc6QcmxJ_O9s3QdGtq5_2P9kGJPz4iQzmSAoQcc8sSYbUtnNNONhkEgR.TpXNA9mOziap92yL0wmL9z16rmgl_C0pA.73aOToV8_wPH7jtXJLtGNgUZc4ku3bw5o54h3yTHah6LkBha4W07IMGP0Okl_1LI1q9U3djdYVuBDreEJ00GD9H8s5FGM0jJ0td_bEynMZHqHnOJAm.kQelXcWZwymKDQWsTf2uH7MdidxIc9FW8VhkU7S69UvhJdByqqB28MiVIKAcd_MGh94RW5m6lva.XU63dpDt6MA6A3mUndbgj_hThNbBV8RDyokRBd8Q4cV5oFstg3cTK6YSkqTtUr2kVQyjETuudoALIDxMrlkK0vkaviTXIGZwW2spg64rQh0yYULWFCOPXNcvQHS2XdXdhXgL2xUjKp5Uq5pVr.z4VvQdrgCZ5mGLSZ1yNX12sFHDB92qzqUaBEhPvQGubfg_P9yLCCL_G_erY_RSfKbdjHVepa0uDIeDGVb.U5hqZHDFFmT9vOyqnt5qWz6n.jFp74hxvh.5nhJQMPpbYiG6EiT1UKk9zimCAEPqdeAzqmTDkwaJaPCw8XZectiDNHccbFZIFYoc.mvEZ2bvV8YuYyNPWeISWfVXaUSN8Vwkj6XpCuhRfM6c7_i6BpyJLFBNVybLuADLlHq4Io4d5hQFWXVP31ZjJUcyoFn2E1kXNajJVefEDs_R.vtcLcF1WtZeHArOmd7XpzQyX5XqTfV_BdjqirGco2XO2kQtfuhbzNja6my1zd0QUqaJPcRdrgg.Mdg5Xrk7_nz5Svgr8w_vySBwMakh8Emac\",mdrd: \"JthMhjMg8rUouLdrBdWCbxdUfHr0QIedcS81mTJmKi8-1751687254-*******-3SjSObDmsQySSWeW55j36xg6RRJeC.MtPNGl_Eqop4bPc3Hth14YugNYFoATt7ncjEutK_IT.RaAgDjjBejUaKnKC2BywnkNB8imcKl4d1kiLyZI4X5UND0lKHJDSxGW614SXcjJD1IjppKDLTTX85ebD.WQ8Ew38zZquhJiGJKgCHMeOEOtUvdERKMu0JpJpzXFNNXfNhkOiR.NwjLpGd99DIXyZJT9bAe97jzXWjHCkuFcBieisxAOZeVugXYTxHvhUv9SFwdk39d78RxJ35xg_UNyAgvcT94ZjwVHHSN64KlPmLry4oHUXipSQdvaKnxUanzp3pwKb0UJBfO7RcSAOydwp6u2N31q8.TzChbi5FimyjxgHXSUvwbExnTTp_ptlMfhr8u4xmNA3EK03twWXIiRoH.9IHl1EXu5ZrErPWwXOBIXTWao.WBEcWH13RwfLHiQmaWzZ74JxowTlWGyYkO_c0ouyFzoJKtZLF78PXc9RhdwKssQet4ayXAy9ZhWYuulC5XNhj2mLxF.4d3uIDcNrwi3CS82_TFz9WrHiQa1JL7MXVldgZ26jsmM2PT1yj_eTzlwzml6epxFNIFBLZq72vzEw0EknBpsnloUlZvX0sWJKjc73YhPngZD.b9jRdKTJDnP9UdLnM3ouEQhOVH4ZYBi3AnAXdlaoW2rPxatTq_x3hfCR_f9NXSpJGCrfzvTvAkEjD4XRIkPPAtwpXzawzZ0ZLI.fZ7OW15_RmnNcNvR_2Z_8DnneFNNJRoUSd0nDkDb4L1nYJc.JFm5zgDpKAJSUjJ62SGs1ZJnMLb3uFr7Rq3GhoH2NiwnOuKXR_BsOrRYfTcnCJNOAxnjm78yDxdEr7a7xcy64EBgUUxUZ3OIz8enzSbUR5JH.UR6Se3hesIsiFdn9h6GOGgPBuOWh8FZoF2TmQ7AE4EY_xrZa5J1foEhg4rFYSdX0fvttIpeZVFGDflSBtem1yJW_FgnPMZunpB.wxKSAzFXzflR2V1LlJz9NF96c0dadBCvrPdsl7yYn2vcnaXDgui.7hSY5Z5ZllTSUaDzYl.R8_VTjcXG6cN9Shvo1nrkaoRMikG4pMzJmeETaAHEUjahG_lSb_SZUtgOcx0fep7t85T_F1ARjJY.21BfoGpLLLFfNjeWcbmDL9lZBDepooS4bLYpb9TxbbrxvZ5rbpVyrvYapfeRbbNXxympbyRJTjY9YCffaUGCMOZSdOdXnZSG5S0fPHG2xoaB_7Cvf5OvYMxJvZOnLRW7TzJpowNbLmWE3cnubUHTYneuxd_vMS.6O_5JBgI.9BGfK7nfT40cgNW3Y8FyjWGHWE3fCBLYqKcvtxXZY3qBNqGYIw0HzZAEzvPIu90AjSQXw1xF0ZA4P766n2WGiibOZrR6i17xT5WOrWKCVUIzRbZJPYc5IMM83T0VsvOiJ05XCFatN5jjFAdGI02l2YH3HLX9lBEQLUhhuGGT5jB3O2Tb0C28dyInB_WyHr00PlvT8IspVGTfp18zN5WOwt_wBu0nFtqPpkVDGnoBSJFvXMtwovMLJAQbY9b3JQIpGqKiZy2Myy_fpmAEvS_M_lM9iOtQsenzMVYF8NbwXPC1Ujq95LQQtixoQBFsYal9P5RdCuSl7WULph7iqVFqmLhiw9xaG_iBAxVnYqC_fDhs3DBpty38_SoWdVfJKtXH91lvBQApYYvUQSk3KtXdgNUXTFMHJHvWI2kebs3xXDDra9VnHhAVs2XZPSfXl0eo6xEwGyyS8U4_QILaussPLN3GhlKA4Iyv3tORxyW0LomHaB3D2xZortv1PLflYZU8fcmlvK7_Xw22IOocXoWzOzSldcw0Y4CUwZ4UlfD.pbagWnu9ZyikHsxnnsAggWoLChZLSQf3nBefl6pvGsNU8AtkToCC0OD7Xgf0FMm9__ZXaCYAc6PwRGGcJ03g69RKnTq1RZ4cRvO6JufAXkDgUur0_p56AaX2JM2EYWgtzGZWuhK1nekgfLjp9_7IQL.mJNrX766mG8Z7r5lUiMbqmxfzD2__.TbjiAk5XKqevBR4S2597BzPaboIA3swupOsAYkAxne3nMnxyb99VrcTp4IQcvhHFcLu5yeWho4IJ0ZvMrzlFYubn9sna2eXcEVUeqrOazVlNgMFKTDHAqw59VDtbSCq.GVlyYq5qk_VEvKNe7UFZb0YY61RQU2PiiC5cDHhgEOc_hAD6soAnZJqohwgI1IUz1RZ8yuO80OqFhXDJgZ8yByynWRtA6HYVoMzJCEbmeNmecCZukZl7LG5q.qSnLgvHtLI3miRTE_Q5jwjYtd4WUkca4l50SVo_6PMzGdj.6yGrgU8dEK8XI2oib4pXz_6zrJM.WXWHdPHZcUmrq4cQ7Pswuvttq5MRLlZw.2ZZ_SQHYO3dtlsfhtVSteAbo0IlC_.RwvFRw0MVJhDL5Ja3UOm11oWCceJ8U3bIei1lmW.p_PigRUWfEVXyqLOoLxkgZj2quFE1kxiqZ4eNGV2zo7HAStP6pJTtR6e25yop1_8MvACXUGT3WK9CBF6alPVr5WNXdfOm9B45Sf6fH99yTROJdyiiK1CC03R0P4zpROf7_A3z08AQ1Xl_ouv94pVdMEfw997dbe2OKRvkwuIxXoAaJaR64IvAiwk5eBuw7opZdblp0bG400K7RbIt79BfdYrR5FnyEamtoT9eX74Gko_cIbXJ0q5NgOKJ3T.a15.znM\",};var a = document.createElement('script');a.src = '/cdn-cgi/challenge-platform/h/b/orchestrate/chl_page/v1?ray=95a3e1be2b8beb2c';window._cf_chl_opt.cOgUHash = location.hash === '' && location.href.indexOf('#') !== -1 ? '#' : location.hash;window._cf_chl_opt.cOgUQuery = location.search === '' && location.href.slice(0, location.href.length - window._cf_chl_opt.cOgUHash.length).indexOf('?') !== -1 ? '?' : location.search;if (window.history && window.history.replaceState) {var ogU = location.pathname + window._cf_chl_opt.cOgUQuery + window._cf_chl_opt.cOgUHash;history.replaceState(null, null, \"\\/doi\\/abs\\/10.1145\\/3679018?__cf_chl_rt_tk=1TEKrWl2xawosfcFG6inA9s16e1_5HyK48LAlrUZsVw-1751687254-*******-RVHJzqUYnWbmlL_yu6wmzdgjOetvyqefbeqvk31F6kY\" + window._cf_chl_opt.cOgUHash);a.onload = function() {history.replaceState(null, null, ogU);}}document.getElementsByTagName('head')[0].appendChild(a);}());</script><script defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95a3e1be2b8beb2c&quot;,&quot;version&quot;:&quot;2025.6.2&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;token&quot;:&quot;b7f168b3cd354a55a4dd51b513830799&quot;,&quot;b&quot;:1}\" crossorigin=\"anonymous\"></script>\n", "<div class=\"footer\" role=\"contentinfo\"><div class=\"footer-inner\"><div class=\"clearfix diagnostic-wrapper\"><div class=\"ray-id\">Ray ID: <code>95a3e1be2b8beb2c</code></div></div><div class=\"text-center\" id=\"footer-text\">性能和安全由<a rel=\"noopener noreferrer\" href=\"https://www.cloudflare.com?utm_source=challenge&amp;utm_campaign=m\" target=\"_blank\">Cloudflare</a>提供</div></div></div></body></html>\n", "获取标题异常: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"h1.citation__title\"}\n", "  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff6b0be6f95+76917]\n", "\tGetHandleVerifier [0x0x7ff6b0be6ff0+77008]\n", "\t(No symbol) [0x0x7ff6b0999dea]\n", "\t(No symbol) [0x0x7ff6b09f0256]\n", "\t(No symbol) [0x0x7ff6b09f050c]\n", "\t(No symbol) [0x0x7ff6b0a43887]\n", "\t(No symbol) [0x0x7ff6b0a184af]\n", "\t(No symbol) [0x0x7ff6b0a4065c]\n", "\t(No symbol) [0x0x7ff6b0a18243]\n", "\t(No symbol) [0x0x7ff6b09e1431]\n", "\t(No symbol) [0x0x7ff6b09e21c3]\n", "\tGetHandleVerifier [0x0x7ff6b0ebd2cd+3051437]\n", "\tGetHandleVerifier [0x0x7ff6b0eb7923+3028483]\n", "\tGetHandleVerifier [0x0x7ff6b0ed58bd+3151261]\n", "\tGetHandleVerifier [0x0x7ff6b0c0185e+185662]\n", "\tGetHandleVerifier [0x0x7ff6b0c0971f+218111]\n", "\tGetHandleVerifier [0x0x7ff6b0befb14+112628]\n", "\tGetHandleVerifier [0x0x7ff6b0befcc9+113065]\n", "\tGetHandleVerifier [0x0x7ff6b0bd6c98+10616]\n", "\tBaseThreadInitThunk [0x0x7ffac747e8d7+23]\n", "\tRtlUserThreadStart [0x0x7ffac7edc34c+44]\n", "\n", "获取摘要异常: Message: no such element: Unable to locate element: {\"method\":\"css selector\",\"selector\":\"div.abstractSection\"}\n", "  (Session info: chrome=138.0.7204.97); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff6b0be6f95+76917]\n", "\tGetHandleVerifier [0x0x7ff6b0be6ff0+77008]\n", "\t(No symbol) [0x0x7ff6b0999dea]\n", "\t(No symbol) [0x0x7ff6b09f0256]\n", "\t(No symbol) [0x0x7ff6b09f050c]\n", "\t(No symbol) [0x0x7ff6b0a43887]\n", "\t(No symbol) [0x0x7ff6b0a184af]\n", "\t(No symbol) [0x0x7ff6b0a4065c]\n", "\t(No symbol) [0x0x7ff6b0a18243]\n", "\t(No symbol) [0x0x7ff6b09e1431]\n", "\t(No symbol) [0x0x7ff6b09e21c3]\n", "\tGetHandleVerifier [0x0x7ff6b0ebd2cd+3051437]\n", "\tGetHandleVerifier [0x0x7ff6b0eb7923+3028483]\n", "\tGetHandleVerifier [0x0x7ff6b0ed58bd+3151261]\n", "\tGetHandleVerifier [0x0x7ff6b0c0185e+185662]\n", "\tGetHandleVerifier [0x0x7ff6b0c0971f+218111]\n", "\tGetHandleVerifier [0x0x7ff6b0befb14+112628]\n", "\tGetHandleVerifier [0x0x7ff6b0befcc9+113065]\n", "\tGetHandleVerifier [0x0x7ff6b0bd6c98+10616]\n", "\tBaseThreadInitThunk [0x0x7ffac747e8d7+23]\n", "\tRtlUserThreadStart [0x0x7ffac7edc34c+44]\n", "\n", "最终标题: 未找到标题\n", "最终摘要: 未找到摘要\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.common.by import By\n", "import time\n", "\n", "# 目标 ACM 论文页面\n", "target_url = \"https://dl.acm.org/doi/abs/10.1145/3679018\"\n", "\n", "# 代理设置（如无代理可注释掉）\n", "proxy = \"http://127.0.0.1:7890\"  # 请替换为你的代理地址和端口\n", "\n", "chrome_options = Options()\n", "chrome_options.add_argument('--headless')  # 无头模式\n", "chrome_options.add_argument(f'--proxy-server={proxy}')  # 设置代理\n", "chrome_options.add_argument('--disable-gpu')\n", "chrome_options.add_argument('--no-sandbox')\n", "\n", "with webdriver.Chrome(options=chrome_options) as driver:\n", "    driver.get(target_url)\n", "    time.sleep(3)  # 等待页面加载，可根据实际情况调整\n", "\n", "    # 输出部分页面源码，便于调试\n", "    print('页面部分源码预览:', driver.page_source)\n", "\n", "    # 保存下来\n", "    with open('page_source.html', 'w', encoding='utf-8') as f:\n", "        f.write(driver.page_source)\n", "\n", "    # 获取标题\n", "    try:\n", "        title = driver.find_element(By.CSS_SELECTOR, 'h1.citation__title').text\n", "        print('获取到的标题:', title)\n", "    except Exception as e:\n", "        print('获取标题异常:', e)\n", "        title = '未找到标题'\n", "\n", "    # 获取摘要\n", "    try:\n", "        abstract = driver.find_element(By.CSS_SELECTOR, 'div.abstractSection').text\n", "        print('获取到的摘要:', abstract)\n", "    except Exception as e:\n", "        print('获取摘要异常:', e)\n", "        abstract = '未找到摘要'\n", "\n", "    print('最终标题:', title)\n", "    print('最终摘要:', abstract)"]}, {"cell_type": "code", "execution_count": null, "id": "e56e196c", "metadata": {}, "outputs": [], "source": ["from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn\n", "from rich.console import Console\n", "from rich.live import Live\n", "from rich.table import Table\n", "\n", "# 多列进度条\n", "with Progress(\n", "    SpinnerColumn(),\n", "    TextColumn(\"[progress.description]{task.description}\"),\n", "    BarColumn(),\n", "    TextColumn(\"[progress.percentage]{task.percentage:>3.0f}%\"),\n", "    TimeElapsedColumn(),\n", ") as progress:\n", "    task = progress.add_task(\"处理文件\", total=100)\n", "    for i in range(100):\n", "        progress.update(task, advance=1)\n", "        time.sleep(0.1)\n", "\n", "# 实时表格更新\n", "console = Console()\n", "table = Table()\n", "table.add_column(\"文件\")\n", "table.add_column(\"状态\")\n", "table.add_column(\"进度\")\n", "\n", "with Live(table, refresh_per_second=4):\n", "    for i in range(10):\n", "        table.add_row(f\"file_{i}.txt\", \"处理中\", f\"{i*10}%\")\n", "        time.sleep(0.5)"]}], "metadata": {"kernelspec": {"display_name": "ccf-crawler", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}